package com.nichesolv.evahanam.evApp.service;

import com.nichesolv.evahanam.charging.repository.ChargingEventDetailsRepository;
import com.nichesolv.evahanam.common.repository.ImageRepository;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.dto.*;
import com.nichesolv.evahanam.evApp.repository.UserVehicleConnectionRepository;
import com.nichesolv.evahanam.evApp.repository.VehicleRiderRepository;
import com.nichesolv.evahanam.telemetryData.repository.bmsrepo.BatteryAlarmRepository;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.jpa.PartHealthLimit;
import com.nichesolv.evahanam.vehicle.repository.FleetRepository;
import com.nichesolv.evahanam.vehicle.repository.PartHealthLimitRepository;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.trip.dto.UserVehicleTripProjection;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripDetailsFields;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.repository.TripDetailsRepo;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import com.nichesolv.evahanam.trip.repository.UserVehicleTripRepository;
import com.nichesolv.evahanam.vehicle.dto.AlarmNameCountProjection;
import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicle.dto.PartHealthDto;
import com.nichesolv.evahanam.vehicle.dto.VehicleHealthResultDto;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.service.VehicleHealthService;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import com.nichesolv.nds.repository.CustomUserRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class UserVehicleService implements IUserVehicleService {
    @Autowired
    CustomUserRepository customUserRepository;

    @Autowired
    UserVehicleConnectionRepository userVehicleConnectionRepository;

    @Autowired
    ImageRepository imageRepository;
    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    EntityManager entityManager;

    @Autowired
    VehicleRiderRepository vehicleRiderRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    TripRepository tripRepository;

    @Autowired
    TripDetailsRepo tripDetailsRepo;

    @Autowired
    UserVehicleTripRepository userVehicleTripRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    UserTripService userTripService;

    @Autowired
    BatteryAlarmRepository batteryAlarmRepository;

    @Autowired
    ChargingEventDetailsRepository chargingEventDetailsRepository;

    @Autowired
    VehicleHealthService vehicleHealthService;

    @Autowired
    FleetRepository fleetRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    PartHealthLimitRepository partHealthLimitRepository;

    @Override
    public List<UserVehicleLastConnectionDetailsDto> findUserVehiclesLastConnectionDetails(CustomUser user, String key, Pageable pageable, OrganisationType organisationType, Long orgId) throws UserNotFoundException {

        List<CustomOrganisation> organisations = customOrganisationRepository.findByUser(user);
        Optional<CustomOrganisation> org = Optional.empty();

        if (organisationType.equals(OrganisationType.B2BCUSTOMER)) {
            org = organisations.stream().filter(e -> e.getOrganisationType().equals(OrganisationType.B2BCUSTOMER)).findFirst();
            if (org.isEmpty()) {
                org = organisations.stream().filter(e -> e.getOrganisationType().equals(OrganisationType.MANUFACTURER)).findFirst();

                if (org.isEmpty()) {
                    organisationType = null;
                }
            }
            organisationType = org.get().getOrganisationType();
        }
        List<IUserVehicleLastConnectionDetailsDto> iUserVehicleLastConnectionDetailsDtoList = new ArrayList<>();
        if (organisationType != null) {
            iUserVehicleLastConnectionDetailsDtoList = userVehicleConnectionRepository.getUserVehiclesLastConnectionDetailsByUserProfileIdAndImeiOrRegNoOrModelName(user.getId(), key, key, key, organisationType.toString(), orgId, pageable);
        }

        return getUserVehicleDtoList(iUserVehicleLastConnectionDetailsDtoList.stream());
    }

    @Override
    public UserVehicleInsightsStaticsDto findUserVehiclesInsightStatistics(CustomUser user, Optional<Long> startTime, Optional<Long> endTime, OrganisationType organisationType, Long orgId) {


        if (organisationType.equals(OrganisationType.B2CCUSTOMER) && vehicleRiderRepository.findByRiderAndIsConnected(user, true).isPresent()) {
            return new UserVehicleInsightsStaticsDto();
        }

        List<List<UserVehicleConnectionDto>> connections = getUserConnections(user, startTime, endTime, orgId);
        List<UserVehicleConnectionDto> todayConnections = connections.get(0);
        List<UserVehicleConnectionDto> previousConnections = connections.get(1);

        Optional<UserVehicleInsightsStaticsDto> todayData = Optional.empty();
        Optional<UserVehicleInsightsStaticsDto> previousData = Optional.empty();

        if (!todayConnections.isEmpty()) {
            todayData = getVehicleRunningMetricsAggregateData(todayConnections, "vehicle_running_metrics");
        }

        if (!previousConnections.isEmpty()) {
            previousData = getVehicleRunningMetricsAggregateData(previousConnections, "vehicle_running_metrics_aggregate");
        }

        UserVehicleInsightsStaticsDto resultData = new UserVehicleInsightsStaticsDto();
        todayConnections.addAll(previousConnections);
        Long chargingTime = null;
        if (!todayConnections.isEmpty()) {
            chargingTime = getAvgChargingTime(todayConnections);
        }
//        if (!previousConnections.isEmpty()) {
//            chargingTime = Optional.ofNullable(chargingTime).orElse(0l) + getAvgChargingTime(previousConnections);
//        }

        if (todayData.isPresent() && previousData.isPresent()) {
            resultData.setDistanceTravelled(Optional.ofNullable(todayData.get().getDistanceTravelled()).orElse(0f) + Optional.ofNullable(previousData.get().getDistanceTravelled()).orElse(0f));
            resultData.setRideDuration(Optional.ofNullable(todayData.get().getRideDuration()).orElse(0L) + Optional.ofNullable(previousData.get().getRideDuration()).orElse(0L));
            resultData.setAvgSpeed((Optional.ofNullable(todayData.get().getAvgSpeed()).orElse(0f) + Optional.ofNullable(previousData.get().getAvgSpeed()).orElse(0f)) / ((todayData.get().getAvgSpeed() == null || todayData.get().getAvgSpeed() == 0f) ? 1f : 2f));
            resultData.setTopSpeed(Math.max(Optional.ofNullable(todayData.get().getTopSpeed()).orElse(0d), Optional.ofNullable(previousData.get().getTopSpeed()).orElse(0d)));
            Map<String, Float> modeRange = todayData.get().getAvgDriveModesRange();

            previousData.get().getAvgDriveModesRange().forEach((key, value) -> {
                if (modeRange.containsKey(key)) {
                    modeRange.replace(key, value + modeRange.get(key));
                } else {
                    modeRange.put(key, value);
                }
            });
            resultData.setAvgDriveModesRange(modeRange);
        } else if (todayData.isPresent()) {
            resultData = todayData.get();
        } else if (previousData.isPresent()) {
            resultData = previousData.get();
        }
        resultData.setChargingTime(Optional.ofNullable(chargingTime).orElse(0L) / 60);

        return resultData;
    }

    @Override
    public UserVehicleInsightsStaticsDetailsDto findUserVehiclesInsightStatisticsDetails(CustomUser user, Optional<Long> startTime, Optional<Long> endTime, String dataType, String period, OrganisationType organisationType, Long orgId) {

        if (organisationType.equals(OrganisationType.B2CCUSTOMER) && vehicleRiderRepository.findByRiderAndIsConnected(user, true).isPresent()) {
            return new UserVehicleInsightsStaticsDetailsDto();
        }
        String[] dataTypes = {"distanceTravelled", "rideDuration", "avgSpeed", "maxSpeed", "chargingTime", "modeRange"};
        Arrays.stream(dataTypes).filter(e -> e.equals(dataType)).findAny().orElseThrow(() -> new IllegalArgumentException(evMessageBundle.getMessage("DATA_TYPE_NOT_MATCHED")));

        List<List<UserVehicleConnectionDto>> connections = getUserConnections(user, startTime, endTime, orgId);
        List<UserVehicleConnectionDto> todayConnections = connections.get(0);
        List<UserVehicleConnectionDto> previousConnections = connections.get(1);

        UserVehicleInsightsStaticsDetailsDto todayData = new UserVehicleInsightsStaticsDetailsDto(null, dataType, new HashMap<>(), new HashMap<>(), new HashMap<>());
        UserVehicleInsightsStaticsDetailsDto previousData = new UserVehicleInsightsStaticsDetailsDto();

        if (!todayConnections.isEmpty() && !dataType.equals("chargingTime")) {
            todayData = getVehicleRunningMetricsAggregateDataDetails(todayConnections, "vehicle_running_metrics", dataType, period);
        }

        if (!previousConnections.isEmpty() && !dataType.equals("chargingTime")) {
            previousData = getVehicleRunningMetricsAggregateDataDetails(previousConnections, "vehicle_running_metrics_aggregate", dataType, period);
        }
        if (dataType.equals("chargingTime") && (!todayConnections.isEmpty() || previousConnections.size() > 0)) {
            todayConnections.addAll(previousConnections);
            todayData = getDailyAvgChargingTime(todayConnections, dataType, period);
        } else {
            if (dataType.equals("modeRange") && Optional.ofNullable(previousData.getModeRangeDataPoints()).isPresent()) {
                Map<Long, Map<String, Float>> dataPointMap = Optional.ofNullable(todayData.getModeRangeDataPoints()).orElse(new HashMap<>());
                dataPointMap.putAll(previousData.getModeRangeDataPoints());
                todayData.setModeRangeDataPoints(dataPointMap);

                Map<String, Float> avgRange = todayData.getAvgModeRange();
                previousData.getAvgModeRange().entrySet().stream().forEach(e -> {
                    if (avgRange.containsKey(e.getKey())) {
                        avgRange.replace(e.getKey(), e.getValue() + avgRange.get(e.getKey()));
                    } else {
                        avgRange.put(e.getKey(), e.getValue());
                    }
                });
                todayData.setAvgModeRange(avgRange);
            } else if (Optional.ofNullable(previousData.getDataPoints()).isPresent()) {
                Map<Long, Object> map = Optional.ofNullable(todayData.getDataPoints()).orElse(new HashMap<>());
                map.putAll(previousData.getDataPoints());
                todayData.setDataPoints(map);
                Object value = null;
                Optional<Float> todayDataValue = Optional.ofNullable((Float) todayData.getValue());
                Optional<Float> previousDataValue = Optional.ofNullable((Float) previousData.getValue());

                if (todayDataValue.isPresent() && previousDataValue.isPresent()) {
                    if (dataType.equals("distanceTravelled") || dataType.equals("rideDuration")) {
                        value = todayDataValue.get() + previousDataValue.get();
                    } else if (dataType.equals("avgSpeed")) {
                        value = (todayDataValue.get() + previousDataValue.get()) / (todayDataValue.isEmpty() || todayDataValue.get() == 0f ? 1f : 2f);
                    } else {
                        value = Math.max(todayDataValue.get(), previousDataValue.get());
                    }
                }

                if (Optional.ofNullable(value).isEmpty()) {
                    todayData.setValue(previousDataValue.get());
                } else {
                    todayData.setValue(value);
                }

            }

        }
        return todayData;
    }

    @Override
    public UserVehicleTestDetailDto getUserVehicleTest(CustomUser user, String identifier) {
        Long userId = user.getId();
        String tripType = TripType.MANUAL.name();
        String status = TestRideSummaryPopulationStatus.COMPLETED.name();
        String fieldName = TripDetailsFields.totalDistance.name();

        List<Trip> userVehicleTestList;
        List<UserVehicleTripProjection> userVehicleTripProjectionsList;
        Optional<Long> vehicleIdOpt = Optional.ofNullable(identifier)
                .map(vehicleService::getVehicleByAnyId)
                .map(Vehicle::getId);

        if (vehicleIdOpt.isPresent()) {
            userVehicleTestList = tripRepository.findTripByTripTypeAndSummaryPopulationStatusAndUserAndImei(tripType, status, userId, vehicleIdOpt.get());
            userVehicleTripProjectionsList = userVehicleTripRepository.findUserVehicleTripHistoryDetailsByImei(status, userId, fieldName, vehicleIdOpt.get(),null);
        } else {
            userVehicleTestList = tripRepository.findTripByTripTypeAndSummaryPopulationStatusAndUser(tripType, status, userId);
            userVehicleTripProjectionsList = userVehicleTripRepository.findUserVehicleTripHistoryDetails(status, userId, fieldName);
        }
        log.debug(" user Vehicle trip Projections size {}", userVehicleTripProjectionsList.size());

        double totalRideDuration = userVehicleTripProjectionsList.stream()
                .filter(projection -> projection.getStartTime() != null && projection.getEndTime() != null)
                .mapToDouble(projection -> Duration.between(projection.getStartTime(), projection.getEndTime()).toSeconds() / 3600.0)
                .sum();
        double totalDistance = userVehicleTripProjectionsList.stream()
                .map(UserVehicleTripProjection::getFieldValue)
                .filter(Objects::nonNull)
                .mapToDouble(value -> {
                    try {
                        return Double.parseDouble(value);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid format for the value : {}", value);
                        return 0.0;
                    }
                }).sum();
        log.debug(" totalRideDuration {} , totalDistance {} ",totalRideDuration , totalRideDuration);
        UserVehicleTestDetailDto userVehicleTestDetailDto = new UserVehicleTestDetailDto();
        userVehicleTestDetailDto.setRideDistance(totalDistance);
        userVehicleTestDetailDto.setRideTime(totalRideDuration);
        userVehicleTestDetailDto.setTestRecords(userVehicleTestList.size());
        return userVehicleTestDetailDto;
    }

    @Override
    public List<UserVehicleTestDetailDto> getUserVehicleTripHistory(String identifier, CustomUser user, Pageable pageable) {
        try {
            log.debug("inside the service method");
            Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
            log.debug(" getting the vehicle id {} ", vehicle.getImei());

            Long userId = user.getId();
            log.debug(" getting the user id {} ", userId);

            List<UserVehicleTripProjection> userTripHistoryProjectionList = userVehicleTripRepository.
                    findUserVehicleTripHistoryDetailsByImei(TestRideSummaryPopulationStatus.COMPLETED.name(), userId, TripDetailsFields.totalDistance.name(), vehicle.getId(), pageable);
            log.debug("the user trip projection size {}", userTripHistoryProjectionList.size());
            List<UserVehicleTestDetailDto> userVehicleTestDetailDtoList = new ArrayList<>();

            for (UserVehicleTripProjection userVehicleTripProjection : userTripHistoryProjectionList) {
                log.debug("user vehicle trip projection {} , {} ", userVehicleTripProjection.getFieldValue(), userVehicleTripProjection.getFieldName());

                UserVehicleTestDetailDto userVehicleTestDetailDto = new UserVehicleTestDetailDto();
                Optional<Trip> manualTrip = tripRepository.
                        findByStartTimeAndEndTimeAndImeiAndSummaryPopulationStatusAndTripType(userVehicleTripProjection.getStartTime(), userVehicleTripProjection.getEndTime(), vehicle.getImei(), TestRideSummaryPopulationStatus.COMPLETED, TripType.MANUAL);

                boolean isManualTrip = manualTrip.isPresent();
                log.debug(" the flag is {}", isManualTrip);
                if (isManualTrip) {
                    Long testId = manualTrip.get().getVehicleTest().getId();
                    userVehicleTestDetailDto.setTestId(testId);
                }
                double totalDistance = Double.parseDouble(userVehicleTripProjection.getFieldValue());
                Long startTime = userVehicleTripProjection.getStartTime().toEpochMilli();
                double rideDuration = Duration.between(userVehicleTripProjection.getStartTime(), userVehicleTripProjection.getEndTime()).toMinutes() / 60.0;
                Long endTime = userVehicleTripProjection.getEndTime().toEpochMilli();
                String startAddress = userTripService.getAddressFromNeighbourhoodAndCityAndState
                        (userVehicleTripProjection.getTripStartNeighbourhood(), userVehicleTripProjection.getTripStartCity(), userVehicleTripProjection.getTripStartState());
                String endAddress = userTripService.getAddressFromNeighbourhoodAndCityAndState(
                        userVehicleTripProjection.getTripEndNeighbourhood(), userVehicleTripProjection.getTripEndCity(), userVehicleTripProjection.getTripEndState());

                userVehicleTestDetailDto.setTripId(userVehicleTripProjection.getId());
                userVehicleTestDetailDto.setRideDistance(totalDistance);
                userVehicleTestDetailDto.setStartTime(startTime);
                userVehicleTestDetailDto.setEndTime(endTime);
                userVehicleTestDetailDto.setRideTime(rideDuration);
                userVehicleTestDetailDto.setIsManual(isManualTrip);
                userVehicleTestDetailDto.setStartAddress(startAddress);
                userVehicleTestDetailDto.setEndAddress(endAddress);
                userVehicleTestDetailDtoList.add(userVehicleTestDetailDto);
            }
            return userVehicleTestDetailDtoList;
        } catch (DataAccessException ex) {
            log.error("Database error while fetching trip history for identifier: {}", identifier, ex);
            throw new IllegalStateException("Database error occurred while fetching trip history", ex);
        } catch (Exception ex) {
            log.error("Unexpected error while listing user-vehicle trips for identifier: {}", identifier, ex);
            throw new RuntimeException("Failed to list the user-trip list with manual flag", ex);
        }
    }

    @Override
    public Object getVehiclePerformanceSummary( Long startTime, Long endTime, String summaryType, Long id, String vehicleType, CustomUser user) {

        Instant start = Instant.ofEpochMilli(startTime);
        Instant end = Instant.ofEpochMilli(endTime);

        return switch (summaryType.toLowerCase()) {
            case "range" -> getRangeData(id, start, end, vehicleType );
            case "motor" -> getMotorData(id, start, end, vehicleType);
            case "battery" -> getBatteryData(id, start, end, vehicleType);
            default -> throw new IllegalArgumentException("Invalid type: " + summaryType);
        };
    }


    private RangeResponseDto getRangeData(Long id, Instant startTime, Instant endTime, String vehicleType) {
        RangeResponseDto dto = new RangeResponseDto();
        dto.setAvgRange(85.0);
        dto.setName("Nds");
        dto.setImei("kgfdkgf45");

        VehicleHealthResultDto vehicleHealthResultDto = vehicleHealthService.getHealth(String.valueOf(id));

        PartHealthDto tyrePartHealth = vehicleHealthResultDto.getParts().stream()
                .filter(part -> "TYRE".equalsIgnoreCase(part.getPartType()))
                .findFirst()
                .orElse(null);

        String tyreHealth =   Optional.ofNullable(tyrePartHealth)
                .map(PartHealthDto::getStatus)
                .orElse("");
        String healthInfo =   Optional.ofNullable(tyrePartHealth)
                .map(PartHealthDto::getInfo)
                .orElse("");
        dto.setTyreHealth(tyreHealth);
        dto.setHealthInfo(healthInfo);

        RangeResponseDto.RangeItem item = new RangeResponseDto.RangeItem();
        item.setRange(80.0);
        item.setTripId(1l);

        List< RangeResponseDto.RangeItem> rangeList = new ArrayList<>();
        rangeList.add(item);
        dto.setAllRanges(rangeList);
        return dto;
    }

    private MotorResponseDto getMotorData(Long id, Instant start, Instant end, String vehicleType) {
        String partType = PartType.MCU.name();
        MotorResponseDto dto = new MotorResponseDto();

        VehicleHealthResultDto vehicleHealthResultDto = vehicleHealthService.getHealth(String.valueOf(id));

        PartHealthDto motorPartHealth = vehicleHealthResultDto.getParts().stream()
                .filter(part ->PartType.MOTOR.name().equalsIgnoreCase(part.getPartType()))
                .findFirst()
                .orElse(null);

        String health =   Optional.ofNullable(motorPartHealth)
                .map(PartHealthDto::getStatus)
                .orElse("");

        String healthInfo =   Optional.ofNullable(motorPartHealth)
                .map(PartHealthDto::getInfo)
                .orElse("");

        dto.setMotorHealth(health);
        dto.setHealthInfo(healthInfo);

        dto.setAlert(Map.of(
                "Motor Temperature", 70,
                "MCS Temperature", 60
        ));


        List<AlarmNameCountProjection> results =
                batteryAlarmRepository.findBatteryAlarms(id, partType, start, end);

        dto.setAlarms(Map.of(
                "Motor Temperature", 70,
                "MCS Temperature", 60
        ));


//        List<BatteryResponseDto.AlarmDto> alarms = results.stream().map(r -> {
//            BatteryResponseDto.AlarmDto alarm = new BatteryResponseDto.AlarmDto();
//            alarm.setKey(r.getName());
//            alarm.setValue(r.getCount());
//            return alarm;
//        }).toList();

        return dto;
    }

    private BatteryResponseDto getBatteryData(Long id, Instant start, Instant end,String vehicleType) {
        BatteryResponseDto dto = new BatteryResponseDto();

        List<Long> vehicleIds = getVehiclesIdByType(id, vehicleType);

        // Get health information for all vehicles
        Map<String, BatteryResponseDto.HealthDetail> healthMap = getBatteryHealthForVehicles(vehicleIds);
        dto.setHealth(healthMap);

        //Avg Charging time
//        String avgTimeToFullCharge = chargingEventDetailsRepository.findAvgChargingTimeForVehicleWithinStartSocLessThanAndEndSocGreaterThan(id,30,100);
//        if (avgTimeToFullCharge != null && !avgTimeToFullCharge.isEmpty()) {
//            dto.setAvgTimeForFullCharge(avgTimeToFullCharge);
//        } else {
//            dto.setAvgTimeForFullCharge("N/A");
//        }

        return dto;
    }

    public Map<String, BatteryResponseDto.HealthDetail> getBatteryHealthForVehicles(List<Long> vehicleIds) {
        Map<String, BatteryResponseDto.HealthDetail> healthMap = new HashMap<>();

        // Get all vehicles by IDs
        List<Vehicle> vehicles = vehicleRepository.findByIdInAndOperationStatus(vehicleIds, OperationStatus.ACTIVE);

        // Get health categories from PartHealthLimit for BATTERY part type
        List<Optional<PartHealthLimit>> healthLimits = getRemarkByPartType(PartType.BATTERY);

        // Initialize health categories with empty lists
        Map<String, List<String>> healthCategories = new HashMap<>();
        for (Optional<PartHealthLimit> limitOpt : healthLimits) {
            if (limitOpt.isPresent()) {
                String remarkName = limitOpt.get().getRemark().name();
                healthCategories.put(remarkName, new ArrayList<>());
            }
        }

        // Process each vehicle to determine its health status
        for (Vehicle vehicle : vehicles) {
            try {
                VehicleHealthResultDto healthResult = vehicleHealthService.getHealthByPartTypes(
                    vehicle.getId().toString(), List.of(PartType.BATTERY));

                if (healthResult.getParts() != null && !healthResult.getParts().isEmpty()) {
                    PartHealthDto batteryHealth = healthResult.getParts().stream()
                        .filter(part -> "BATTERY".equalsIgnoreCase(part.getPartType()))
                        .findFirst()
                        .orElse(null);

                    if (batteryHealth != null && batteryHealth.getStatus() != null) {
                        String healthStatus = batteryHealth.getStatus();
                        healthCategories.computeIfAbsent(healthStatus, k -> new ArrayList<>())
                            .add(vehicle.getImei());
                    }
                }
            } catch (Exception e) {
                // Log error and continue with next vehicle
                log.warn("Failed to get health status for vehicle ID: {}, error: {}", vehicle.getId(), e.getMessage());
            }
        }

        // Calculate percentages and create HealthDetail objects
        int totalVehicles = vehicles.size();
        for (Map.Entry<String, List<String>> entry : healthCategories.entrySet()) {
            String category = entry.getKey();
            List<String> imeiList = entry.getValue();

            int count = imeiList.size();
            int percentage = totalVehicles > 0 ? Math.round((count * 100.0f) / totalVehicles) : 0;

            BatteryResponseDto.HealthDetail healthDetail = new BatteryResponseDto.HealthDetail();
            healthDetail.setValue(percentage);
            healthDetail.setImei(imeiList.stream().map(Long::parseLong).collect(Collectors.toList()));

            healthMap.put(category, healthDetail);
        }

        return healthMap;
    }


    public List<Long> getVehiclesIdByType( Long id,String type) {
        return switch (type.toLowerCase()) {
            case "fleet" -> getVehicleIdsByFleetId(id);
            case "model" -> getVehicleIdsByModelId(id);
            case "vehicle" -> List.of(id);
            default -> throw new IllegalArgumentException("Invalid vehicle type: " + type);
        };
    }

    public List<Optional<PartHealthLimit>> getRemarkByPartType(PartType partType)
    {
        return partHealthLimitRepository.findByPartType(partType);
    }

    private List<Long> getVehicleIdsByFleetId(Long fleetId) {
        return fleetRepository.getFleetVehicleModelsWithVehicleImei(fleetId)
                .stream()
                .map(projection -> Long.parseLong(projection.getVehId()))
                .collect(Collectors.toList());
    }

    private List<Long> getVehicleIdsByModelId(Long modelId) {
        return vehicleRepository.findVehicleIdsByVehicleModelId(modelId);
    }


    private UserVehicleInsightsStaticsDetailsDto getVehicleRunningMetricsAggregateDataDetails(List<UserVehicleConnectionDto> connections, String entityName, String dataType, String period) {
        String timeColumnName;
        String dataIntervalType;
        if (Optional.ofNullable(period).isPresent() && period.equals("day")) {
            dataIntervalType = "hour";
        } else {
            dataIntervalType = "day";
        }
        if (entityName == "vehicle_running_metrics_aggregate") {
            timeColumnName = "time_bucket";
        } else {
            timeColumnName = "timestamp";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        int length = connections.size();
        String timeBucketSubQuery = "";
        for (int i = 0; i < length; i++) {
            timeBucketSubQuery = timeBucketSubQuery.concat("( " + timeColumnName + " between \'" + connections.get(i).getStartOn().atZone(ZoneId.systemDefault()).toLocalDateTime().format(formatter) + "\' and \'" + connections.get(i).getEndOn().atZone(ZoneId.systemDefault()).toLocalDateTime().format(formatter) + "\' and imei = \'" + connections.get(i).getImei() + "\' )");
            if (i != length - 1) {
                timeBucketSubQuery = timeBucketSubQuery.concat(" or ");
            }
        }
        UserVehicleInsightsStaticsDetailsDto userVehicleInsightsStaticsDetailsDto = new UserVehicleInsightsStaticsDetailsDto();
        if (dataType.equals("modeRange")) {
            Map<Long, Map<String, Float>> map = new HashMap<>();
            Map<String, Float> modeTotalDistance = new HashMap<>();
            Map<String, Float> modeCalDischarge = new HashMap<>();

            String queryString2 = getQueryStringForModeRange(timeBucketSubQuery, entityName, timeColumnName, dataIntervalType);
            log.info(queryString2);
            Query query = entityManager.createNativeQuery(queryString2, Tuple.class);
            List<Tuple> result = query.getResultList();
            AtomicReference<Long> epoch = new AtomicReference<>();

            result.stream().filter(e -> e.get(1) != null && e.get(2) != null).forEach(e -> {

                        String key = e.get("driveModeName", String.class);
                        Float distanceTravelled = e.get("distanceTravelled", Double.class).floatValue();
                        Float calDischarge = e.get("calDischarge", Double.class).floatValue();


                        if (!key.equals("NULL_DRIVE_SELECTION") && calDischarge != 0f) {
                            Float value = (distanceTravelled / calDischarge) * 100;

                            epoch.set(e.get("interval", Instant.class).toEpochMilli());
                            if (modeTotalDistance.containsKey(key)) {
                                Float range = modeTotalDistance.get(key);
                                modeTotalDistance.replace(key, (range + distanceTravelled.floatValue()));
                            } else {
                                modeTotalDistance.put(key, distanceTravelled);
                            }
                            if (modeCalDischarge.containsKey(key)) {
                                Float range = modeCalDischarge.get(key);
                                modeCalDischarge.replace(key, (range + calDischarge.floatValue()));
                            } else {
                                modeCalDischarge.put(key, calDischarge);
                            }
                            map.computeIfAbsent(epoch.get(), k -> new HashMap<>()).put(key, value);

                        }

                    }
            );
            Map<String, Float> avgModeRange = new HashMap<>();
            modeTotalDistance.entrySet().stream().forEach(
                    e -> {
                        avgModeRange.put(e.getKey(), (e.getValue() / modeCalDischarge.get(e.getKey())) * 100);
                    }
            );

            userVehicleInsightsStaticsDetailsDto.setDataPoints(new HashMap<>());
            userVehicleInsightsStaticsDetailsDto.setModeRangeDataPoints(map);
            userVehicleInsightsStaticsDetailsDto.setAvgModeRange(avgModeRange);
        } else {
            Map<Long, Object> map = new HashMap<>();
            String queryString1 = getQueryString(entityName, dataType, timeColumnName, dataIntervalType);
            queryString1 = queryString1.concat(timeBucketSubQuery + " GROUP BY interval ORDER BY interval DESC");
            log.info(queryString1);
            Query query1 = entityManager.createNativeQuery(queryString1, Tuple.class);
            List<Tuple> result1 = query1.getResultList();


            switch (dataType) {
                case "distanceTravelled":
                    result1.stream().filter(e -> e.get(1) != null).forEach(e -> {
                        map.putIfAbsent(e.get("interval", Instant.class).toEpochMilli(), e.get("distanceTravelled", Double.class));
                    });
                    userVehicleInsightsStaticsDetailsDto.setValue((float) map.values().stream().map(value -> (Double) value).mapToDouble(Double::floatValue).sum());
                    break;
                case "rideDuration":
                    result1.stream().filter(e -> e.get(1) != null).forEach(e -> {
                        Long value = (Long) e.get("rideDuration");
                        map.putIfAbsent(e.get("interval", Instant.class).toEpochMilli(), (value / 60f));
                    });
                    userVehicleInsightsStaticsDetailsDto.setValue((float) Math.round(map.values().stream().map(value -> (Float) value).mapToDouble(Float::floatValue).sum()));
                    break;
                case "avgSpeed":
                    AtomicReference<Double> totalDistance = new AtomicReference<>(0d);
                    AtomicReference<Long> totalDuration = new AtomicReference<>(0l);
                    result1.stream().filter(e -> e.get(1) != null && e.get(2) != null).forEach(e -> {
                        Double distance = e.get("distanceTravelled", Double.class);
                        Long duration = (Long) e.get("rideDuration");
                        totalDuration.updateAndGet(v -> v + duration);
                        totalDistance.updateAndGet(v -> v + distance);
                        Float avgSpeed = distance.floatValue() / duration.floatValue();
                        map.putIfAbsent(e.get("interval", Instant.class).toEpochMilli(), avgSpeed * 60 * 60);
                    });
                    userVehicleInsightsStaticsDetailsDto.setValue(totalDistance.get().floatValue() / (totalDuration.get() == 0d ? 1f : totalDuration.get().floatValue()) * 60 * 60);
                    break;
                case "maxSpeed":
                    result1.stream().filter(e -> e.get(1) != null).forEach(e -> {
                        map.putIfAbsent(e.get("interval", Instant.class).toEpochMilli(), e.get("maxSpeed", Double.class));
                    });
                    OptionalDouble max = map.values().stream().map(value -> (Double) value).mapToDouble(Double::floatValue).max();
                    userVehicleInsightsStaticsDetailsDto.setValue(max.isPresent() ? (float) max.getAsDouble() : 0f);
                    break;
            }
            userVehicleInsightsStaticsDetailsDto.setModeRangeDataPoints(new HashMap<>());
            userVehicleInsightsStaticsDetailsDto.setDataPoints(map);
        }
        userVehicleInsightsStaticsDetailsDto.setDataType(dataType);
        return userVehicleInsightsStaticsDetailsDto;
    }

    private static String getQueryStringForModeRange(String timeBucketSubQuery, String entityName, String timeColumnName, String dataIntervalType) {

        String queryString2 = "SELECT drive_mode_range.interval, drive_mode_range.distanceTravelled as distanceTravelled, drive_mode_range.calDischarge as calDischarge, drive_mode.name as driveModeName FROM (select distinct DATE_TRUNC('" + dataIntervalType + "', " + timeColumnName + ") AS interval, drive_mode_id, CAST(SUM(distance_travelled) AS FLOAT) as distanceTravelled, CAST(SUM(cal_discharge)AS FLOAT) as calDischarge from " + entityName + " where drive_mode_id is not null and cal_discharge > 0 and cal_discharge is not null and (";
        queryString2 = queryString2.concat(timeBucketSubQuery + ") group by  interval,  drive_mode_id order by interval DESC, drive_mode_id ) as drive_mode_range, drive_mode where drive_mode.id=drive_mode_range.drive_mode_id");
        return queryString2;
    }

    private static String getQueryString(String entityName, String dataType, String timeColumnName, String dataIntervalType) {
        String dataTypeSubQuery = "";
        switch (dataType) {
            case "distanceTravelled":
                dataTypeSubQuery = " CAST(SUM(distance_travelled) AS FLOAT) AS distanceTravelled ";
                break;
            case "rideDuration":
                dataTypeSubQuery = (entityName.equals("vehicle_running_metrics_aggregate") ? "CAST(SUM(ride_duration) AS BIGINT) as rideDuration" : " count(distance_travelled) * 10 as rideDuration");
                break;
            case "avgSpeed":
                dataTypeSubQuery = " CAST(SUM(distance_travelled) AS FLOAT) AS distanceTravelled, " + (entityName.equals("vehicle_running_metrics_aggregate") ? "CAST(SUM(ride_duration) AS BIGINT) as rideDuration" : " count(distance_travelled) * 10 as rideDuration");
                break;
            case "maxSpeed":
                dataTypeSubQuery = " CAST(max(max_speed) AS FLOAT) AS maxSpeed";
                break;
        }
        String queryString1 = "SELECT DATE_TRUNC('" + dataIntervalType + "', " + timeColumnName + ") AS interval, " + dataTypeSubQuery + " FROM  " + entityName + " WHERE ";
        return queryString1;
    }

    private List<List<UserVehicleConnectionDto>> getUserConnections(CustomUser user, Optional<Long> startTime, Optional<Long> endTime, Long orgId) {
        if (!startTime.isPresent() || !endTime.isPresent()) {
            throw new IllegalArgumentException("Start time and end time must be present");
        }

        Instant start2 = Instant.ofEpochMilli(startTime.get());
        Instant end2 = Instant.ofEpochMilli(endTime.get());
        Instant start1 = null;
        Instant end1 = null;

        log.debug("Initial start2: {}, end2: {}", start2, end2);


        // Get today's date at start of the day
        LocalDate localDate = LocalDate.now();
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        Instant startOfDayInstant = zonedDateTime.toInstant();

        log.debug("Start of today: {}", startOfDayInstant);

        // Separate today's time and after today's time
        if (end2.isAfter(startOfDayInstant)) {
            if (!start2.isBefore(startOfDayInstant)) {
                start1 = start2;
                end1 = end2;
                start2 = null;
                end2 = null;
                log.debug("After separation (all today): start1: {}, end1: {}, start2: {}, end2: {}", start1, end1, start2, end2);
            } else {
                end1 = end2;
                start1 = startOfDayInstant;
                end2 = startOfDayInstant;
                log.debug("After separation (crossing today): start1: {}, end1: {}, start2: {}, end2: {}", start1, end1, start2, end2);
            }
        }
        List<UserVehicleConnectionProjection> todayConnectionProjections = new ArrayList<>();
        if (start1 != null && end1 != null) {
            todayConnectionProjections = userVehicleConnectionRepository.findByStartOnOREndOnAndUser(user.getId(), start1.atZone(ZoneId.systemDefault()).toLocalDateTime(), end1.atZone(ZoneId.systemDefault()).toLocalDateTime(), start1.atZone(ZoneId.systemDefault()).toLocalDateTime(), end1.atZone(ZoneId.systemDefault()).toLocalDateTime(), start1.atZone(ZoneId.systemDefault()).toLocalDateTime(), end1.atZone(ZoneId.systemDefault()).toLocalDateTime(), orgId);
        }
        List<UserVehicleConnectionProjection> previousConnectionProjections = new ArrayList<>();
        if (start2 != null && end2 != null) {
            previousConnectionProjections = userVehicleConnectionRepository.findByStartOnOREndOnAndUser(user.getId(), start2.atZone(ZoneId.systemDefault()).toLocalDateTime(), end2.atZone(ZoneId.systemDefault()).toLocalDateTime(), start2.atZone(ZoneId.systemDefault()).toLocalDateTime(), end2.atZone(ZoneId.systemDefault()).toLocalDateTime(), start2.atZone(ZoneId.systemDefault()).toLocalDateTime(), end2.atZone(ZoneId.systemDefault()).toLocalDateTime(), orgId);
        }

        Instant finalStart1 = start1;
        Instant finalEnd1 = end1;
        Instant finalStart2 = start2;
        Instant finalEnd2 = end2;
        List<UserVehicleConnectionDto> todayUserVehicleConnections = new ArrayList<>();
        List<UserVehicleConnectionDto> previousUserVehicleConnections = new ArrayList<>();

        todayConnectionProjections.forEach(e -> {
            UserVehicleConnectionDto userVehicleConnection = new UserVehicleConnectionDto();

            if (e.getEndOn() == null) {
                userVehicleConnection.setEndOn(finalEnd1);
            } else {
                userVehicleConnection.setEndOn(e.getEndOn());
            }
            if (e.getStartOn().isBefore(finalStart1)) {
                userVehicleConnection.setStartOn(finalStart1);
            } else {
                userVehicleConnection.setStartOn(e.getStartOn());
            }
            userVehicleConnection.setVehicleId(e.getVehicleId());
            userVehicleConnection.setImei(e.getImei());
            todayUserVehicleConnections.add(userVehicleConnection);
        });

        previousConnectionProjections.forEach(e -> {
            UserVehicleConnectionDto userVehicleConnection1 = new UserVehicleConnectionDto();

            if (e.getEndOn() == null) {
                userVehicleConnection1.setEndOn(finalEnd2);
            } else if (e.getEndOn().isAfter(finalEnd2)) {
                userVehicleConnection1.setEndOn(finalEnd2);
            } else {
                userVehicleConnection1.setEndOn(e.getEndOn());
            }
            if (e.getStartOn().isBefore(finalStart2)) {
                userVehicleConnection1.setStartOn(finalStart2);
            } else {
                userVehicleConnection1.setStartOn(e.getStartOn());
            }
            if (!e.getStartOn().isAfter(finalEnd2)) {
                userVehicleConnection1.setImei(e.getImei());
                previousUserVehicleConnections.add(userVehicleConnection1);

            }

        });

        return Arrays.asList(todayUserVehicleConnections, previousUserVehicleConnections);
    }

    private Long getAvgChargingTime(List<UserVehicleConnectionDto> connections) {
        String queryString = "SELECT count(vehicle_state) * 10 as chargingTime  from vehicle_status WHERE vehicle_state=\'CHARGING\' and ( ";
        int length = connections.size();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < length; i++) {
            queryString = queryString.concat("( timestamp between \'" + connections.get(i).getStartOn().atZone(ZoneId.systemDefault()).format(formatter) + "\' and \'" + connections.get(i).getEndOn().atZone(ZoneId.systemDefault()).format(formatter) + "\' and imei = \'" + connections.get(i).getImei() + "\' )");
            if (i != length - 1) {
                queryString = queryString.concat(" or ");
            } else {
                queryString = queryString.concat(" ) ");
            }
        }
        log.info(queryString);
        Query query = entityManager.createNativeQuery(queryString, Long.class);

        List<Long> results = query.getResultList();
        return results.get(0);
    }

    private UserVehicleInsightsStaticsDetailsDto getDailyAvgChargingTime(List<UserVehicleConnectionDto> connections, String dataType, String period) {
        String dataIntervalType;
        if (Optional.ofNullable(period).isPresent() && period.equals("day")) {
            dataIntervalType = "hour";
        } else {
            dataIntervalType = "day";
        }
        String queryString = "SELECT DATE_TRUNC('" + dataIntervalType + "', timestamp) AS interval, count(vehicle_state) * 10 as chargingTime  from vehicle_status WHERE vehicle_state=\'CHARGING\' and ( ";
        int length = connections.size();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < length; i++) {
            queryString = queryString.concat("( timestamp between \'" + connections.get(i).getStartOn().atZone(ZoneId.systemDefault()).format(formatter) + "\' and \'" + connections.get(i).getEndOn().atZone(ZoneId.systemDefault()).format(formatter) + "\' and imei = \'" + connections.get(i).getImei() + "\' )");
            if (i != length - 1) {
                queryString = queryString.concat(" or ");
            } else {
                queryString = queryString.concat(" ) GROUP BY interval ORDER BY interval DESC");
            }
        }
        log.info(queryString);
        Query query = entityManager.createNativeQuery(queryString, Tuple.class);
        List<Tuple> results = query.getResultList();
        Map<Long, Object> map = new HashMap<>();
        UserVehicleInsightsStaticsDetailsDto userVehicleInsightsStaticsDetailsDto = new UserVehicleInsightsStaticsDetailsDto();
        userVehicleInsightsStaticsDetailsDto.setDataType(dataType);
        results.stream().filter(e -> e.get(1) != null).forEach(e -> {
            map.putIfAbsent(e.get("interval", Instant.class).toEpochMilli(), (e.get("chargingTime", Long.class) / 60f));
        });
        userVehicleInsightsStaticsDetailsDto.setValue(map.values().stream().filter(value -> value instanceof Float).map(value -> (Float) value).mapToDouble(Float::doubleValue).sum());
        userVehicleInsightsStaticsDetailsDto.setDataPoints(map);
        return userVehicleInsightsStaticsDetailsDto;
    }

    private Optional<UserVehicleInsightsStaticsDto> getVehicleRunningMetricsAggregateData(List<UserVehicleConnectionDto> connections, String entityName) {

        String queryString1 = "SELECT CAST(sum(distance_travelled) AS FLOAT) as distanceTravelled," + (entityName.equals("vehicle_running_metrics_aggregate") ? "CAST(SUM(ride_duration) AS BIGINT) as rideDuration" : " count(distance_travelled) * 10 as rideDuration") + ", max(max_speed) as maxSpeed  from " + entityName + " WHERE ";
        String queryString2 = "SELECT drive_mode.name as driveModeName, drive_mode_range.range FROM (SELECT distinct(drive_mode_id), CAST((SUM(distance_travelled)/SUM(cal_discharge)*100) AS FLOAT) as range FROM " + entityName + " where drive_mode_id is not null and cal_discharge > 0 and cal_discharge is not null and (";
        int length = connections.size();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeColumnName;
        if (Objects.equals(entityName, "vehicle_running_metrics_aggregate")) {
            timeColumnName = "time_bucket";
        } else {
            timeColumnName = "timestamp";
        }
        String timeBucketSubQuery = "";
        for (int i = 0; i < length; i++) {
            timeBucketSubQuery = timeBucketSubQuery.concat("( " + timeColumnName + " between \'" + connections.get(i).getStartOn().atZone(ZoneId.systemDefault()).format(formatter) + "\' and \'" + connections.get(i).getEndOn().atZone(ZoneId.systemDefault()).format(formatter) + "\' and imei = \'" + connections.get(i).getImei() + "\' )");
            if (i != length - 1) {
                timeBucketSubQuery = timeBucketSubQuery.concat(" or ");
            }
        }
        queryString1 = queryString1.concat(timeBucketSubQuery);
        queryString2 = queryString2.concat(timeBucketSubQuery + ") group by drive_mode_id) as drive_mode_range, drive_mode where drive_mode.id=drive_mode_range.drive_mode_id");

        log.info(queryString1);
        log.info(queryString2);

        Query query1 = entityManager.createNativeQuery(queryString1, Tuple.class);
        Query query2 = entityManager.createNativeQuery(queryString2, Tuple.class);

        List<Tuple> result1 = query1.getResultList();
        List<Tuple> result2 = query2.getResultList();

        Map<String, Float> driveModeMap = getDriveModeRangeFromResult(result2);

        Optional<UserVehicleInsightsStaticsDto> userVehicleInsightsStaticsDto = result1.stream()
                .map(this::mapTupleToDto)
                .findFirst();

        userVehicleInsightsStaticsDto.get().setAvgDriveModesRange(driveModeMap);

        return userVehicleInsightsStaticsDto;
    }

    private Map<String, Float> getDriveModeRangeFromResult(List<Tuple> result) {
        Map<String, Float> map = new HashMap<>();

        result.stream().filter(e -> e.get(1) != null).forEach(e -> {
            map.putIfAbsent(e.get("driveModeName", String.class), e.get("range", Double.class).floatValue());
        });
        return map;
    }

    private UserVehicleInsightsStaticsDto mapTupleToDto(Tuple tuple) {
        UserVehicleInsightsStaticsDto userVehicleInsightsStaticsDto = new UserVehicleInsightsStaticsDto();
        Double rideDuration = Optional.ofNullable(tuple.get("rideDuration", Long.class)).orElse(0l) / 60d;
        Double distance = Optional.ofNullable(tuple.get("distanceTravelled", Double.class)).orElse(0d);
        userVehicleInsightsStaticsDto.setDistanceTravelled(distance.floatValue());
        userVehicleInsightsStaticsDto.setRideDuration(Math.round(rideDuration));
        Double avgSpeed = distance / (rideDuration == 0L || rideDuration == null ? 1l : rideDuration / 60d);
        userVehicleInsightsStaticsDto.setAvgSpeed(Optional.ofNullable(avgSpeed).isPresent() ? avgSpeed.floatValue() : null);
        userVehicleInsightsStaticsDto.setTopSpeed(tuple.get("maxSpeed", Double.class));
        return userVehicleInsightsStaticsDto;
    }


    List<UserVehicleLastConnectionDetailsDto> getUserVehicleDtoList(Stream<IUserVehicleLastConnectionDetailsDto> iUserVehicleLastConnectionDetailsDtoList) {
        List<UserVehicleLastConnectionDetailsDto> userVehicleLastConnectionDetailsDtoList = iUserVehicleLastConnectionDetailsDtoList.map(e -> {
            return new UserVehicleLastConnectionDetailsDto(Optional.ofNullable(e.getRegNo()).orElse(e.getImei()), e.getModelName(), getImagesFromSetOfId(e.getImageId()), e.getSoc(), e.getDistanceCovered(), e.getStartOn().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), e.getEndOn() == null ? null : e.getEndOn().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }).collect(Collectors.toList());
        return userVehicleLastConnectionDetailsDtoList;
    }

    private List<ImageDto> getImagesFromSetOfId(String imageIds) {
        List<Long> imageIdList = Arrays.stream(imageIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        return imageRepository.findByIdIn(imageIdList).stream().map(e -> new ImageDto(e.getTag(), e.getUrl())).collect(Collectors.toList());
    }
}
