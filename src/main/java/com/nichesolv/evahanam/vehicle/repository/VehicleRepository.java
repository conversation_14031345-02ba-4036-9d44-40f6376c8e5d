package com.nichesolv.evahanam.vehicle.repository;

import com.nichesolv.evahanam.telemetryData.dto.AvailableVehicleLocationData;
import com.nichesolv.evahanam.vehicle.dto.VehicleLocationProjection;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdsProjection;
import com.nichesolv.evahanam.vehicle.dto.VehicleStateCountProjection;
import com.nichesolv.evahanam.vehicle.dto.VehiclePartModelProjection;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMetaData;
import com.nichesolv.evahanam.vehicle.dto.metadata.VehicleMinMaxData;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.enums.VehicleIdentifierTypes;
import com.nichesolv.evahanam.vehicle.jpa.Part;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.dto.DataTypeLongProjection;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import jakarta.persistence.QueryHint;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;

public interface VehicleRepository extends JpaRepository<Vehicle, Long> {

    @Query(value = "select v.mfr_org_id,v.id from vehicle v where not exists(select 1 from fleet f where f.fleet_name = concat('MOTHER_FLEET-',v.mfr_org_id) and v.mfr_org_id = f.owner_org_id)",nativeQuery = true)
    List<Object[]> getVehiclesWithoutMotherFleet();

    /**
     * Query to check if a vehicle exists by one of the identifiers <li>chassis number</li><li>imei</li> <li>vehicle id</li><li>registration number</li>
     * @param identifier
     * @return <Optional>Vehicle</Optional>
     */
    @Query(value = "WITH vehicle_identifier(identifier,mfr_id,dealer_id,owner_id ) AS (VALUES (:identifier,:mfr_id,:dealer_id,:owner_id)) " +
            "SELECT EXISTS ( " +
            "   SELECT 1 " +
            "   FROM vehicle v " +
            "   LEFT JOIN vehicle_registration_details vr ON v.id = vr.vehicle_id " +
            "   JOIN vehicle_identifier vi ON vi.identifier = CASE " +
            "       WHEN vi.identifier = v.chassis_number THEN v.chassis_number " +
            "       WHEN vi.identifier = v.imei THEN v.imei " +
            "       WHEN vi.identifier = CAST(v.id AS varchar) THEN CAST(v.id AS varchar) " +
            "       ELSE vr.registration_number " +
            "   END AND v.mfr_org_id = vi.mfr_id OR v.dealership_id = vi.dealer_id OR v.owner_org_id = vi.owner_id" +
            ")", nativeQuery = true)
    boolean existsByVehicleIdentifierAndManufacturerOrDealershipOrOwner(@Param("identifier") String identifier, @Param("mfr_id") Long mfrId, @Param("dealer_id") Long dealerId, @Param("owner_id") Long ownerId);

    /**
     * Query to return all vehicle identifiers of a given manufacturer or dealer
     * @param mfrId
     * @param dealerId
     * @return <List>VehicleIdsProjection</List>
     */
    @Query(value = "select vrd.registration_number as vehRegNo,v.id as vehId,v.chassis_number as vehChassisNo,v.imei as vehImei from vehicle_registration_details vrd join vehicle v on v.id = vrd.vehicle_id AND v.mfr_org_id =?1 or dealership_id=?1", nativeQuery = true)
    List<VehicleIdsProjection> getVehicleIdentifiersByManufacturerIdOrDealershipId(Long mfrId,Long dealerId);
    /**
     * Query to check if a vehicle exists by one of the identifiers <li>chassis number</li><li>imei</li> <li>vehicle id</li><li>registration number</li>
     * @param identifier
     * @return <Optional>Vehicle</Optional>
     */
    @Query(value = "WITH vehicle_identifier(var1) AS (VALUES (:identifier)) " +
            "SELECT EXISTS ( " +
            "   SELECT 1 " +
            "   FROM vehicle v " +
            "   LEFT JOIN vehicle_registration_details vr ON v.id = vr.vehicle_id " +
            "   JOIN vehicle_identifier vi ON vi.var1 = CASE " +
            "       WHEN vi.var1 = v.chassis_number THEN v.chassis_number " +
            "       WHEN vi.var1 = v.imei THEN v.imei " +
            "       WHEN vi.var1 = CAST(v.id AS varchar) THEN CAST(v.id AS varchar) " +
            "       ELSE vr.registration_number " +
            "   END" +
            ")", nativeQuery = true)
    boolean existsByVehicleIdentifier(@Param("identifier") String identifier);

    /**
     * Query to get a vehicle by one of the identifiers <li>chassis number</li><li>imei</li> <li>vehicle id</li><li>registration number</li>
     * @param identifier
     * @return <Optional>Vehicle</Optional>
     */
    @Query(value = "WITH vehicle_identifier(var1) AS (VALUES (:identifier)) " +
            "SELECT v.* " +
            "FROM vehicle v " +
            "LEFT JOIN vehicle_registration_details vr ON v.id = vr.vehicle_id " +
            "JOIN vehicle_identifier vi ON vi.var1 = CASE " +
            "    WHEN vi.var1 = v.chassis_number THEN v.chassis_number " +
            "    WHEN vi.var1 = v.imei THEN v.imei " +
            "    WHEN vi.var1 = CAST(v.id AS varchar) THEN CAST(v.id AS varchar) " +
            "    ELSE vr.registration_number " +
            "END", nativeQuery = true)
    Optional<Vehicle> findVehicleByIdentifier(@Param("identifier") String identifier);

    /**
     * Query to get all vehicle identifiers for a vehicle manufacturer
     * @param organisation
     * @return <Page>VehicleIdentifiers</Page>
     */
    @Query(value = "select coalesce(vrd.registration_number,v.imei) as vehRegNo,v.id as vehId,coalesce(v.chassis_number,v.imei) as vehChassisNo,v.imei as vehImei from vehicle_registration_details vrd right outer join vehicle v on v.id = vrd.vehicle_id WHERE v.mfr_org_id =?1 and v.operation_status='ACTIVE'", nativeQuery = true)
    Page<VehicleIdsProjection> getVehicleIdentifiersByOrg(Long organisation,Pageable pageable);

    /**
     * Query to get all vehicle identifiers for a vehicle manufacturer
     * @param identifier
     * @return <Optional>VehicleIdentifiers</Optional>
     */
    @Query(value = "WITH vehicle_identifier(var1) AS (VALUES (:identifier))  " +
            "            SELECT " +
            "               (case when vr.registration_number is null then v.imei else vr.registration_number end ) as vehRegNo" +
            "               ,v.id as vehId" +
            "               ,v.chassis_number as vehChassisNo" +
            "               ,v.imei as vehImei " +
            "            FROM vehicle v  " +
            "            LEFT JOIN vehicle_registration_details vr ON v.id = vr.vehicle_id  " +
            "            JOIN vehicle_identifier vi ON vi.var1 = CASE  " +
            "                WHEN vi.var1 = v.chassis_number THEN v.chassis_number  " +
            "                WHEN vi.var1 = v.imei THEN v.imei  " +
            "                WHEN vi.var1 = CAST(v.id AS varchar) THEN CAST(v.id AS varchar)  " +
            "                ELSE vr.registration_number  " +
            "            END", nativeQuery = true)
    Optional<VehicleIdsProjection> getVehicleIdentifiers(@Param("identifier") String identifier);

    @Query(value="WITH vehicle_identifier(var1,identifier_type) AS ( VALUES (:pattern,:identifier))  \n" +
            "SELECT \n" +
            "    CASE \n" +
            "        WHEN vr.registration_number IS NULL THEN v.imei \n" +
            "        ELSE vr.registration_number \n" +
            "    END AS vehRegNo,\n" +
            "    v.id AS vehId,\n" +
            "    v.chassis_number AS vehChassisNo,\n" +
            "    v.imei AS vehImei \n" +
            "FROM vehicle v  \n" +
            "LEFT JOIN vehicle_registration_details vr \n" +
            "    ON v.id = vr.vehicle_id  \n" +
            "JOIN vehicle_identifier vi \n" +
            "    ON (\n" +
            "        case \n" +
            "            when vi.identifier_type='veh_chassis_no' then v.chassis_number LIKE '%' || vi.var1 || '%'  -- Matching on chassis_number\n" +
            "            when vi.identifier_type='veh_imei' then  v.imei LIKE '%' || vi.var1 || '%'           -- Matching on imei\n" +
            "            when vi.identifier_type='veh_reg_no' then  vr.registration_number LIKE '%' || vi.var1 || '%'  -- Matching on registration_number\n" +
            "            else false\n" +
            "        end\n" +
            "    )\n" +
            "WHERE v.mfr_org_id = :mfr_org_id and v.operation_status='ACTIVE'",nativeQuery = true)
    Page<VehicleIdsProjection> getVehiclesOfPatternByOrg(@Param("mfr_org_id") Long organisation, @Param("pattern") String pattern, @Param("identifier") String idType,  Pageable pageable);

    boolean existsByChassisNumber(String chassisNumber);

    boolean existsByImei(String imei);

    Optional<Vehicle> findByImei(String imei);

    List<Vehicle> findByIdInAndOperationStatus(List<Long> ids, OperationStatus operationStatus);

    Optional<Vehicle> findByDeviceAdvertisingName(String deviceAdvertisingName);

    Optional<Vehicle> findByVehicleParts(Part vehiclePart);

    List<Vehicle> findByImeiIn(List<String> vehicleImei);

    Optional<Vehicle> findFirstByImeiEndsWithOrderByMfrDateDesc(String vehicleImeiCode);

    Optional<Vehicle> findFirstByChassisNumberEndsWithOrderByMfrDateDesc(String chassisCode);

    Optional<Vehicle> findByChassisNumberEndsWithAndImeiEndsWith(String chassisCode, String imeiCode);
    Optional<Vehicle> findByChassisNumberAndImei(String chassisCode, String imeiCode);

    Page<Vehicle> findAllByManufacturer(CustomOrganisation customOrganisation, Pageable pageable);

    List<Vehicle> findAllByManufacturerAndOperationStatus(CustomOrganisation customOrganisation, OperationStatus operationStatus);

    boolean existsByManufacturerOrDealershipAndImeiOrId(CustomOrganisation customOrganisation, CustomOrganisation customOrganisation1, String imei, Long id);

    boolean findByIdAndManufacturerOrDealership(Long id, CustomOrganisation customOrganisation, CustomOrganisation customOrganisation1);

    boolean existsByImeiAndManufacturerOrDealership(String imei, CustomOrganisation customOrganisation, CustomOrganisation customOrganisation1);

    boolean existsByIdAndManufacturerOrDealership(Long id, CustomOrganisation customOrganisation, CustomOrganisation customOrganisation1);

    @Query(value = "select count(v.imei) as vehicleCount, sum(v.total_distance_traveled) " +
            "as totalDistance, max(v.total_distance_traveled) " +
            "as maxTravelledDistance, min(v.total_distance_traveled) " +
            "as minTravelledDistance " +
            "from vehicle v where v.dealership_id=?1 or mfr_org_id=?1", nativeQuery = true)
    VehicleMetaData getVehicleMetaData(Long id);

    @Query(value = "select imei, COALESCE(v.total_distance_traveled,0) as distance,v.imei as vehImei,v.id as vehId,v.chassis_number as vehChassisNo,vrd.registration_number as vehRegNo from " +
    "vehicle v left join vehicle_registration_details vrd on vrd.vehicle_id = v.id where  v.total_distance_traveled is not null and mfr_org_id=:orgId or dealership_id=:dealerId", nativeQuery = true)
    List<VehicleMinMaxData> getTopTenRunningVehicleByAscOrDesc(@Param("orgId") Long organisationId, @Param("dealerId") Long dealerId, Pageable pageable);

    List<Vehicle> findByManufacturerOrDealership(CustomOrganisation organisation, CustomOrganisation organisation1);

    @Query(value = "select imei from vehicle where mfr_org_id= ?1 or dealership_id= ?2 ",nativeQuery = true)
    List<String> findImeiByManufacturerOrDealership(Long mfrOrgId,Long dealershipId);

    List<Vehicle> findByManufacturerOrDealershipOrOwner(CustomOrganisation organisation, CustomOrganisation dealer, CustomOrganisation owner);

    @Query(value = "select imei from view_vehicle_telemetry where ?1", nativeQuery = true)
    List<String> findByFieldNameAndPage(String threshold, Pageable sortByDesc);

    Optional<Vehicle> findByChassisNumber(String chassisNumber);

    @Query(value = "select imei from vehicle where chassis_number is null", nativeQuery = true)
    List<String> getVehiclesWithChassisNull();

    @QueryHints(value = {
            @QueryHint(name = HINT_FETCH_SIZE, value = "600"),
    })
    Stream<Vehicle> findAllByOperationStatus(OperationStatus operationStatus);

    @Query(value = "SELECT imei FROM vehicle WHERE operation_status = ?1", nativeQuery = true)
    List<String> getVehicleImeiWithOperationStatus(String operationStatus);

    @Query(value = "SELECT imei, location_updated_at as timestamp, latitude, longitude FROM vehicle v, organisations o WHERE o.id = v.owner_org_id AND v.location_updated_at is not null AND v.latitude is not null and v.longitude is not null and v.owner_org_id = ?1 and v.mfr_org_id = ?6 AND v.latitude BETWEEN ?2 AND ?3 AND v.longitude BETWEEN ?4 AND ?5", nativeQuery = true)
    List<AvailableVehicleLocationData> getLocationDataByLatitudeBetweenAndLongitudeBetweenByOwner(Long orgId, Float startLat, Float endLat, Float startLong, Float endLong, Long mfrOrgId);

    Vehicle findVehicleStateByImeiAndManufacturerAndOperationStatus(String imei, CustomOrganisation customOrganisation, OperationStatus operationStatus);

    @Query(value = "SELECT imei from vehicle where mfr_org_id =?1 and operation_status = 'ACTIVE' and vehicle_state= ?2 ", nativeQuery = true)
    Page<String> getVehicleByStatus(Long id, String status, Pageable pageable);

    @Query(value = "SELECT vrd.registration_number as vehRegNo,v.id as vehId,v.chassis_number as vehChassisNo,v.imei as vehImei " +
            "from vehicle v " +
            "left join vehicle_registration_details vrd   on v.id = vrd.vehicle_id " +
            "WHERE v.mfr_org_id = ?1 " +
            "AND v.operation_status = 'ACTIVE' " +
            "AND v.vehicle_state = ?2 "
             , nativeQuery = true)
    Page<VehicleIdsProjection> getVehiclesByStatus(Long id, String status, Pageable pageable);

//vrd.registration_number as vehRegNo,v.id as vehId,v.chassis_number as vehChassisNo,v.imei as vehImei
    @Query(value = "SELECT v.imei FROM vehicle v " +
            "INNER JOIN fleet_vehicle fv ON (v.id = fv.vehicle_id) " +
            "INNER JOIN fleet f ON (fv.fleet_id = f.id) " +
            "WHERE v.mfr_org_id = ?1 " +
            "AND v.operation_status = 'ACTIVE' " +
            "AND v.vehicle_state = ?2 " +
            "AND f.id = ?3 ", nativeQuery = true)
    Page<String> getFleetVehicleByStatus(Long manufacturerId, String vehicleState, Long fleetId, Pageable pageable);


    /***
     * Returns all vehicle identifiers by fleet id , manufacturer id and vehicle state
     * @param manufacturerId
     * @param vehicleState
     * @param fleetId
     * @param pageable
     * @return <Page>VehicleIdsProjection></Page>
     */
    @Query(value = "SELECT vrd.registration_number as vehRegNo,v.id as vehId,v.chassis_number as vehChassisNo,v.imei as vehImei " +
            "from vehicle v " +
            "left join vehicle_registration_details vrd   on v.id = vrd.vehicle_id " +
            "INNER JOIN fleet_vehicle fv ON (v.id = fv.vehicle_id) " +
            "INNER JOIN fleet f ON (fv.fleet_id = f.id) " +
            "WHERE v.mfr_org_id = ?1 " +
            "AND v.operation_status = 'ACTIVE' " +
            "AND v.vehicle_state = ?2 " +
            "AND f.id = ?3 ", nativeQuery = true)
    Page<VehicleIdsProjection> getFleetVehiclesByStatus(Long manufacturerId, String vehicleState, Long fleetId, Pageable pageable);

    @Query(value = " SELECT vehicle_state AS vehicleState, COUNT(imei) AS stateCount " +
            "FROM vehicle WHERE mfr_org_id = ?1 " +
            "AND operation_status = 'ACTIVE'" +
            "GROUP BY vehicle_state", nativeQuery = true)
    List<VehicleStateCountProjection> getStateCount(Long orgId);

    @Query(value = "SELECT vehicle_state AS vehicleState, COUNT(imei) AS stateCount FROM vehicle " +
            "INNER JOIN fleet_vehicle ON vehicle.id = fleet_vehicle.vehicle_id " +
            "INNER JOIN fleet ON fleet_vehicle.fleet_id = fleet.id " +
            "WHERE vehicle.mfr_org_id = ?1 " +
            "AND vehicle.operation_status = 'ACTIVE' " +
            "AND fleet.id = ?2 " +
            "GROUP BY vehicle_state", nativeQuery = true)
    List<VehicleStateCountProjection> getStateCountForFleet(Long orgId, Long id);

    @Query(value = "SELECT pm.id, pm.part_type AS partType, pm.name FROM vehicle_parts vp, part p, part_model pm WHERE vp.vehicle_id=?1 AND vp.part_id=p.id AND p.part_model_id=pm.id", nativeQuery = true)
    List<VehiclePartModelProjection> getVehiclePartModels(Long vehicleId);

    @Query(value = "SELECT p.part_model_id as id FROM vehicle_parts vp, part p where vp.vehicle_id=?1 and vp.part_id=p.id and p.part_type=?2 ", nativeQuery = true)
    Optional<DataTypeLongProjection> getPartModelIdByVehicleIdAndPartType(Long vehicleId, String partType);

    List<Vehicle> findByUser(CustomUser user);

    @Query(value ="SELECT vehicle_id FROM vehicle_parts WHERE part_id = ?1" ,nativeQuery = true)
    Optional<Long> findVehicleByPartId(Long id);

    @Query(value = "SELECT imei FROM vehicle", nativeQuery = true)
    List<String> getVehicleImei();

    @Query(value = "SELECT mfr_org_id from vehicle where imei = ?1", nativeQuery = true)
    Long getManufacturerId(String imei);

    @Query(value = "select imei,latitude,longitude from vehicle where imei in ?1 ",nativeQuery = true)
    List<VehicleLocationProjection> findLocationByImeiIn(List<String> imei);

    @Query(value = "SELECT v.id FROM vehicle v WHERE v.vehicle_model_id = ?1 AND v.operation_status = 'ACTIVE'", nativeQuery = true)
    List<Long> findVehicleIdsByVehicleModelId(Long vehicleModelId);

}
